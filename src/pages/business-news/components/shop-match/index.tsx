import { useRequest, useUnmount } from 'ahooks';
import DataCard from '../card-list/card';
import { Flex, Form, Select } from 'antd';
import { useDataState } from '../store/useDataState';
import { useEffect, useState } from 'react';
import RedSelect from '../common/red-select';
import { MyShop, OtherShop } from './shop';
import HeatGraph from '@/components/charts/amap/heat-graph';
import { queryData, queryShopCompareSelectData, queryShopCompareData } from '@/services';
import { getFields } from '../handle-data/datas';
import { createPortal } from 'react-dom';
import LevelIcon from '@/components/level-icon';

const radiusOptions = [
  { label: '附近2km同业分布图', value: 2 },
  { label: '附近10km同业分布图', value: 10 },
  { label: '附近20km同业分布图', value: 20 },
];

export default function ShopMatch() {
  const {
    shopIdList,
    dateRange,
    isFood,
    isShow,
    registerField,
    unregisterField,
    isChild,
    isMultiShops,
    pid,
    shopList,
  } = useDataState();
  const [selectedShop, setSelectedShop] = useState<{ label?: string; value?: string }>({});

  const fields: any[] = isFood
    ? getFields(
        [
          '门店曝光量',
          '门店访问量',
          '预约到店量',
          '电话拨打量',
          '广告有效到店量',
          '广告有效到店成本',
        ],
        {},
        true,
      )
    : getFields(
        ['门店曝光量', '来电量', '广告客资量', '广告客资成本', '广告有效到店量'],
        {},
        false,
      ).concat({
        title: '商家分',
        key: 'business_score',
      });

  const shownFields = isShow(fields.map((item) => `同行竞争_${item.key}`));

  const filterFields = fields.filter((item, index) => shownFields[index]);

  const { data: sameTypeShopList = [] } = useRequest(
    async () => {
      const startDate = dateRange?.[0];
      const endDate = dateRange?.[1];

      if (!startDate || !endDate || !shopList?.length || !shopIdList?.length) {
        return [];
      }

      try {
        const res = await queryShopCompareSelectData({
          shopId: shopIdList[0], // 传入当前选中的第一个门店ID
          startDate,
          endDate,
        });

        const list: Array<{
          match_shopid: string;
          match_shopname: string;
          score_level?: string;
          shop_id: string;
        }> = res?.applicationDataList?.[0]?.ruleDataList?.[0]?.values || [];

        const resList = list.map((item) => ({
          label: item.match_shopname,
          value: item.match_shopid,
          score_level: item.score_level,
        }));

        if (resList.length > 0) {
          setSelectedShop(resList[0]);
        }
        return resList;
      } catch (error) {
        console.error('对比门店筛选请求失败:', error);
        return [];
      }
    },
    {
      refreshDeps: [shopList, dateRange],
    },
  );

  const { data: dataResults = [] } = useRequest(
    async () => {
      const startDate = dateRange?.[0];
      const endDate = dateRange?.[1];

      if (startDate && endDate && selectedShop?.value && shopIdList?.length) {
        try {
          const res = await queryShopCompareData({
            shopIdList: [shopIdList[0], selectedShop.value],
            businessNewType: isFood ? 'FOOD' : 'OTHER',
            startDate,
            endDate,
          });

          const resultData = res?.applicationDataList?.[0]?.ruleDataList?.[0]?.values || [];

          return resultData;
        } catch (error) {
          console.error('门店对比数据请求失败:', error);
          return [];
        }
      }
      return [];
    },
    {
      refreshDeps: [selectedShop?.value, dateRange],
    },
  );
  const showGraph = isShow('shop_match_graph');

  const [radius, setRadius] = useState(radiusOptions[0].value);

  const { data: yuntuData } = useRequest(
    async () => {
      const [startDate, endDate] = dateRange || [];
      if (isMultiShops || !startDate || !endDate || !shopIdList.length) return {};
      const res = await queryData(
        {
          shopIdList,
          pid,
          startDate: dateRange?.[0],
          endDate: dateRange?.[1],
          buffer: radius,
        },
        'xibao_report_yuntu_same_tag_graph_application',
      );
      const result = res.applicationDataList?.[0]?.ruleDataList?.[0]?.values?.[0];
      const center = result?.company_xy ? result.company_xy.split(',') : [];
      return {
        ...result,
        center,
      };
    },
    {
      refreshDeps: [shopList, pid, dateRange, radius],
    },
  );

  useEffect(() => {
    registerField({
      label: '同行竞争',
      key: 'shop_match',
      children: [
        {
          title: '同业门店分布图',
          key: 'shop_match_graph',
        },
        {
          key: 'distance',
          render: () => {
            return (
              <RedSelect
                placeholder="附近同行业门店范围"
                options={radiusOptions}
                value={radius}
                onChange={setRadius}
                style={{ marginRight: 12 }}
              />
            );
          },
        },
        ...fields.map((item) => ({
          key: `同行竞争_${item.key}`,
          title: item.title,
        })),
      ],
    });
  }, [isFood, radius]);
  useUnmount(() => {
    unregisterField('shop_match');
  });

  if (!isChild || (!shownFields.some(Boolean) && !showGraph)) {
    return null;
  }

  return (
    <DataCard
      data={{
        label: (
          <Flex>
            同行竞争
            {createPortal(
              <Form.Item label="选择对比门店">
                <Select
                  options={sameTypeShopList}
                  labelInValue
                  allowClear
                  value={selectedShop}
                  onChange={setSelectedShop}
                  placeholder="选择门店"
                  optionRender={(option) => (
                    <span>
                      {option.data.label}
                      {option.data.score_level && (
                        <LevelIcon
                          level={option.data.score_level}
                          size={18}
                          style={{ marginLeft: 6 }}
                        />
                      )}
                    </span>
                  )}
                />
              </Form.Item>,
              document.getElementById('weekly-form-portal'),
            )}
          </Flex>
        ),
      }}
    >
      <div style={{ padding: '0 1.2em 1.2em' }}>
        {showGraph && (
          <div style={{ position: 'relative' }}>
            <HeatGraph
              center={yuntuData?.center || []}
              radius={radius}
              data={yuntuData?.same_tag_graph || ''}
              flag
            />
            <div
              style={{
                position: 'absolute',
                left: 14,
                top: 5,
                color: '#09b0c1',
                background: '#fff',
                padding: '2px 10px',
                borderRadius: 4,
                fontSize: '1.2em',
              }}
            >
              周边{radius}km同业分布{' '}
            </div>
          </div>
        )}
        {!shownFields.some(Boolean) ? null : (
          <Flex
            gap={10}
            style={{
              backgroundImage: `url('https://img.alicdn.com/imgextra/i3/O1CN011ruhqO1K4t3IMzJH2_!!6000000001111-2-tps-2028-186.png')`,
              backgroundSize: '100% 45px',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <MyShop
              fields={filterFields}
              data={dataResults?.[0] || {}}
              compareData={dataResults?.[1] || {}}
            />
            <OtherShop fields={filterFields} shop={selectedShop} data={dataResults?.[1] || {}} />
          </Flex>
        )}
      </div>
    </DataCard>
  );
}
