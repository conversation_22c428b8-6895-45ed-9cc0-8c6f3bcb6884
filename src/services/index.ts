import { ActionScene, DemotionSceneEnum } from '@/constants';
import { IActivity } from '@/types';
import request, { gdRequest } from './request';
import { queryFirstShopTotalCount } from '@alife/mo-select-shop-m';
import { Dayjs } from 'dayjs';
import { IBusinessBaseData } from '@/pages/business-news/components/const';
import { IPageInfo, ITaskResponse } from '@/pages/index/components/task-detail-drawer/types';
import { ITodoTask } from '@/pages/index/components/todo-list/types';
import * as EspAuditTypes from '@/types/esp-audit';
import { ITaskCompletionRateData } from '@/types/task-completion-rate';

// const testShopIdList = ['2024090411077000000110244077|xiniu_xgc_bgc'];

interface IViewConfig {
  type: string;
  show: boolean;
  desc?: string;
  grey?: string;
  jumpUrl?: string;
  client?: string;
  jumpType?: string;
  jumpTypeNew?: string;
  jumpUrlList?: string;
}

interface IViewResponse {
  view: Record<string, IViewConfig[]>;
}

// 分场景下发按钮
export const getActionList = (params: {
  sceneList: ActionScene[];
  viewOperatorId?: string;
}): Promise<IViewResponse> => {
  return gdRequest('amap-sales-operation.OptConfigQueryHsf.queryViewConfig', params);
};
// 推广通充值
export const recharge = (params: { amount: string; pid: string }): Promise<{}> => {
  return request('amap.sales.operation.AdOperationManageFacade.adRechargeApply', params);
};
// 推广通活动
export const getActivityList = (params: { pid: string; amount?: number }): Promise<IActivity> => {
  return request('amap.sales.operation.AdOperationManageFacade.adActivityConsult', params);
};

// 获取喜报
export const queryMerchantBusinessNews = (params: {
  pid?: number | string;
  shopIdList: string[];
  startDate: Dayjs;
  endDate: Dayjs;
}): Promise<IBusinessBaseData> => {
  return request('amap-sales-operation.AgentOperationQueryFacade.queryMerchantBusinessNews', {
    ...params,
    startDate: params.startDate.valueOf(),
    endDate: params.endDate.valueOf(),
  });
};

export const queryData = (
  params: {
    pid?: number | string;
    shopIdList: string[];
    startDate: Dayjs;
    endDate: Dayjs;
    [key: string]: any;
  },
  code: string,
) => {
  // if (params.shopIdList.length === 1) {
  //   params.shopIdList = testShopIdList;
  // }
  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: code,
    requestChannel: 'XUANYUAN',
    resultType: 'LIST',
    requestParams: {
      ...params,
      startDate: params.startDate.format('YYYYMMDD'),
      endDate: params.endDate.format('YYYYMMDD'),
      shopIdList: params.shopIdList.join(','),
    },
  });
};

// 查询门店汇总数据 - 对应右边数据展示
export const queryShopOverviewData = async (params: {
  pid?: number | string;
  shopIdList?: string[];
  startDate: Dayjs;
  endDate: Dayjs;
  [key: string]: any;
}) => {
  const requestParams: any = {
    ...params,
    startDate: params.startDate.format('YYYYMMDD'),
    endDate: params.endDate.format('YYYYMMDD'),
  };

  // 只有当 shopIdList 存在时才添加到请求参数中
  if (params.shopIdList) {
    requestParams.shopIdList = params.shopIdList.join(',');
  }

  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: 'xibao_shop_overview_0819',
    requestChannel: 'XUANYUAN',
    resultType: 'LIST',
    requestParams,
  });
};

// 查询门店明细数据 - 对应左边左下角门店明细列表
export const queryShopDetailData = async (params: {
  pid?: number | string;
  shopIdList: string[];
  startDate: Dayjs;
  endDate: Dayjs;
  pageSize?: number;
  pageNo?: number;
  [key: string]: any;
}) => {
  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: 'xibao_shop_detail_0819',
    requestChannel: 'XUANYUAN',
    resultType: 'PAGE',
    pageSize: params.pageSize || 10,
    pageNo: params.pageNo || 1,
    requestParams: {
      ...params,
      startDate: params.startDate.format('YYYYMMDD'),
      endDate: params.endDate.format('YYYYMMDD'),
      shopIdList: params.shopIdList.join(','),
    },
  });
};

// 对比门店筛选 - 获取可对比的门店列表
export const queryShopCompareSelectData = async (params: {
  shopId: string;
  startDate: Dayjs;
  endDate: Dayjs;
  [key: string]: any;
}) => {
  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: 'xibao_shop_same_atag_select_0819',
    requestChannel: 'XUANYUAN',
    resultType: 'LIST',
    requestParams: {
      ...params,
      startDate: params.startDate.format('YYYYMMDD'),
      endDate: params.endDate.format('YYYYMMDD'),
    },
  });
};

// 同业门店对比数据 - 获取门店对比数据
export const queryShopCompareData = async (params: {
  shopIdList: string[];
  businessNewType: string;
  startDate: Dayjs;
  endDate: Dayjs;
  [key: string]: any;
}) => {
  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: 'xibao_shop_same_atag_data_compare_0819',
    requestChannel: 'XUANYUAN',
    resultType: 'LIST',
    requestParams: {
      ...params,
      startDate: params.startDate.format('YYYYMMDD'),
      endDate: params.endDate.format('YYYYMMDD'),
      shopIdList: params.shopIdList.join(','),
    },
  });
};

// 发起智能诊断
export const startDiagnose = async (params: {
  shopIdList?: string;
  pid?: string;
  startDate: Dayjs;
  endDate: Dayjs;
}) => {
  const requestParams: any = {
    startDate: params.startDate.format('YYYYMMDD'),
    endDate: params.endDate.format('YYYYMMDD'),
  };

  if (params.shopIdList) {
    requestParams.shopIdList = params.shopIdList;
  } else if (params.pid) {
    requestParams.pid = params.pid;
  }

  return gdRequest('amap-sales-data.SmartDiagnoseGwFacade.startDiagnose', {
    sceneCode: 'XIBAO_SMART_DIAGNOSE',
    requestChannel: 'XUANYUAN',
    params: requestParams,
  });
};

// 轮询诊断结果
export const queryDiagnoseResult = async (taskId: string) => {
  return gdRequest('amap-sales-data.SmartDiagnoseGwFacade.queryDiagnoseResult', {
    taskId,
  });
};

// 查询同行数据
export const queryPeersData = async (params: {
  shopIdList?: string[];
  pid?: string;
  startDate: Dayjs;
  endDate: Dayjs;
  range: 'NORMAL' | 'CITY' | 'DISTRICT';
  peerKeyList: string;
}) => {
  return request('amap-sales-data.DataQueryGwFacade.queryData', {
    applicationCode: 'xibao_shop_peers_0819',
    requestChannel: 'XUANYUAN',
    resultType: 'LIST',
    requestParams: {
      ...params,
      shopIdList: params.shopIdList?.join(','), // 多个门店ID用逗号分隔
      startDate: params.startDate.format('YYYYMMDD'),
      endDate: params.endDate.format('YYYYMMDD'),
    },
  });
};

export const queryUserInfo = () =>
  request('alsc-merchant-core.AlscMerchantBusinessDataClient.queryUserInfo');

export const queryFisrtShop = async () => {
  const res = await queryFirstShopTotalCount();
  // 类型断言，假设返回值包含firstShop和totalCount字段
  const result = res as any;
  const { firstShop, totalCount } = result || {};
  const { kbShopId, storeName } = firstShop || {};
  return { firstShopId: kbShopId, firstShopName: storeName, total: totalCount };
};

export const getMerchantReviewExtInfo = (pid: string, scene?: string) => {
  return gdRequest('amap-sales-operation.OperationAutoPushFacade.queryMerchantDetailExtra', {
    pid,
    scene,
  });
};

export const getDemotionSceneQuery = async (
  scene: DemotionSceneEnum,
): Promise<{
  demotionResult: 'DEGRADE' | 'NORMAL';
}> => {
  const res = await request('amap.sales.operation.OptConfigQueryHsf.demotionSceneQuery', {
    scene: [scene],
  });
  return res?.[0];
};

export const configBusinessNewsGrey = (
  sceneList: Array<
    'TASK_PRIORITY' | 'OPT_ESP_ORDER_CREATE_GREY' | 'AI_SUPPLY' | 'OPT_ESP_ORDER_BATCH_SUBMIT_GREY'
  >,
): Promise<
  Array<{
    switchStatus: boolean;
    scene: string;
  }>
> => {
  return request('amap-sales-operation.OptConfigQueryHsf.businessSceneSwitch', {
    sceneList,
  });
};

// 获取任务名称和待发送商户数
export const getTaskInfo = (): Promise<{
  waitReachMerchantSummaryDTOList: Array<{
    scene: string;
    waitReachPidNum: number;
  }>;
}> => {
  return gdRequest('amap-sales-operation.OperationAutoPushFacade.queryWaitReachMerchantSummary');
};

// 获取限时任务概览
export const getUndoTaskList = (
  params: any,
): Promise<{
  dataList: Array<{
    merchantId: string;
    merchantName: string;
    mainShopName: string;
    staffConfirmNoReachTag: boolean;
    greyTag: boolean;
  }>;
  pageInfo: {
    currentPageNo: number;
    pageSize: number;
    totalCount: number;
    totalPage: number;
    hasMore: boolean;
    nextPageNo: number;
  };
}> => {
  return gdRequest('amap-sales-operation.OperationAutoPushFacade.queryWaitReachMerchantList', {
    ...params,
  });
};

// 限时任务-圈选
export const checkoutTask = (params: {
  needSendPidList: string[];
  noNeedSendPidList: string[];
}) => {
  return gdRequest('amap-sales-operation.OperationAutoPushFacade.confirmMerchantReach', params);
};

// 增加一个查询联系人列表接口
export const queryContactList = (params: {
  page: number;
  pageSize: number;
  entityId: string;
  entityType: string;
  scene: string;
}) => {
  return gdRequest('mtop.alsc.kbt.leads.center.kp.pageQuery', {
    sourceFrom: 'XY_PC_AGENT_SHOP',
    ...params,
  });
};

// 开启广告新手加速计划
export const enableAdPlan = (params: {
  shopId: string;
  orderId: string;
  advertiserId: string;
  campaignId: string;
}) => {
  return gdRequest('amap-sales-operation.AdOperationManageFacade.updateTgtCampaignInfo', params);
};

// 获取充值历史
export const getRechargeHistory = (params: {
  merchantId: string;
  page: any;
}): Promise<{
  pageInfo: {
    totalCount: number;
  };
  dataList: Array<{
    displayText: string;
    submitTime: string;
  }>;
}> => {
  return gdRequest('amap-sales-operation.AdOperationManageFacade.queryTgtRechargeRecord', params);
};

// 查询推广计划情况
export const queryAdPlan = (params: {
  shopId: string;
}): Promise<{
  campaignId: string;
  orderId: string;
  startTime: string;
  canOptUpdate: boolean;
  advertiserId: string;
} | null> => {
  return gdRequest('amap-sales-operation.AdOperationManageFacade.queryTgtCampaignInfo', params);
};

// 查询预警任务数据
export const queryWarningTaskList = (params: {
  pid: string;
  warningTaskType: string;
  page: any;
}): Promise<{
  pageInfo: {
    totalCount: number;
  };
  dataList: Array<{
    displayText: string;
    submitTime: string;
  }>;
}> => {
  return gdRequest(
    'amap-sales-operation.MerchantWarningTaskManageFacade.queryWarningTaskList',
    params,
  );
};

// 定义预警任务状态的枚举
export enum WarningTaskStatus {
  UN_DONE = 'UN_DONE', // 未处理
  DOING = 'DOING', // 处理中
  UNABLE_TO_PROCESS = 'UNABLE_TO_PROCESS', // 无法处理
  EXPIRED = 'EXPIRED', // 已逾期
  DONE = 'DONE', // 已完成
}
export enum WarningTaskStatusMap {
  已完成 = WarningTaskStatus.DONE,
  处理中 = WarningTaskStatus.DOING,
  无法处理 = WarningTaskStatus.UNABLE_TO_PROCESS,
  已逾期 = WarningTaskStatus.EXPIRED,
  未处理 = WarningTaskStatus.UN_DONE,
}

// 任务状态枚举颜色
export const warningTaskColorMap = {
  [WarningTaskStatus.UN_DONE]: 'red', // 未处理
  [WarningTaskStatus.DOING]: '#1677ff', // 处理中
  [WarningTaskStatus.UNABLE_TO_PROCESS]: 'red', // 无法处理
  [WarningTaskStatus.EXPIRED]: 'red', // 已逾期
  [WarningTaskStatus.DONE]: 'green', // 已完成
};
// 定义附件类型的枚举
enum AttachmentType {
  PICTURE = 'PICTURE', // 图片
}

// 定义附件列表中的对象结构
interface Attachment {
  type: AttachmentType;
  content: string; // 图片链接
}

// 定义主要的接口
interface WarningTask {
  pid: string; // 必填商户PID
  warningTaskNo: string; // 必填预警任务ID（对应任务实例taskNo）
  warningReason: string; // 必填预警原因
  solution: string; // 必填解决方案
  warningTaskStatus: WarningTaskStatus; // 必填预警任务状态
  attachmentList?: Attachment[]; // 非必填附件列表
}

// 提交预警任务数据
export const submitWarningTask = (params: WarningTask): Promise<{}> => {
  return gdRequest(
    'amap-sales-operation.MerchantWarningTaskManageFacade.handleWarningTask',
    params,
  );
};

// 获取待办、广告任务、预警任务等数据，因为结构体一样，所以都复用这一个方法
export const getTaskData = (
  type: '商家分+续签' | '广告任务' | '预警任务',
): Promise<{
  [key: string]: Array<{
    property: string;
    value: string;
    rightBoundary?: string;
    rightSymbol?: string;
    codes?: string[];
    desc?: string;
    unit?: string;
    [key: string]: any;
  }>;
}> => {
  const urlMap = {
    '商家分+续签': 'amap-sales-operation.OptMatterQueryFacade.queryShopTaskNumsInfo',
    广告任务: 'amap-sales-operation.OptMatterQueryFacade.queryMerchantTaskNumsInfo',
    预警任务: 'amap-sales-operation.OptMatterQueryFacade.queryMerchantWarnTaskNumsInfo',
  };
  if (!urlMap[type]) return Promise.reject('type参数错误');
  return gdRequest(urlMap[type]);
};

export const getCallRecordMissingVisit = async (params: any) => {
  return gdRequest(
    'alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.getCallRecordMissingVisit',
    params,
  );
};

export const submitShopCollect = (
  shopId: string,
  collectShopName?: string,
  dataTypes?: string[],
) => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.submitShopCollect', {
    shopId,
    collectShopName,
    dataTypes,
  });
};
// 查询门店采买信息
export const queryShopCollectInfo = (shopId: string) => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryShopCollectInfo', {
    shopId,
  });
};

export const getTodoTaskList = (params: {
  taskType: string;
  page: {
    pageNo: number;
    pageSize: number;
  };
}): Promise<{
  dataList: ITodoTask[];
  pageInfo: IPageInfo;
}> => {
  return gdRequest('amap-sales-operation.OptMatterQueryFacade.queryAgentTodoTaskList', params);
};
// 推进去完成的状态
export const pushTodoState = (taskNo: string) => {
  return gdRequest('amap-sales-operation.OptTaskManageFacade.startOptTask', {
    taskNo,
  });
};
export const getQueryMerchantPriorityTaskInfo = (params: {
  page: {
    pageSize: number;
    pageNo: number;
  };
  pid: string;
}): Promise<ITaskResponse> => {
  return gdRequest(
    'amap-sales-operation.AgentOperationQueryFacade.queryMerchantPriorityTaskInfo',
    params,
  );
};
export const createAiReportSummary = (params: {
  pid: number | string;
  shopIdList: string[];
  startDate: Dayjs;
  endDate: Dayjs;
  bizSource: 'business_news' | 'merchant_analysis';
}): Promise<{
  taskNo: string;
}> => {
  const startDate = params.startDate ? params.startDate.valueOf() : undefined;
  const endDate = params.endDate ? params.endDate.valueOf() : undefined;
  return gdRequest('amap-sales-operation.OptAnalysisToolFacade.analysis', {
    startDate,
    endDate,
    shopIdList: params.shopIdList,
    pid: params.pid,
    bizSource: params.bizSource,
  });
};

export const queryAiResult = (params: {
  taskNo: string;
}): Promise<{
  analysisTxt: string;
}> => {
  return gdRequest('amap-sales-operation.OptAnalysisToolFacade.queryAnalysisInfo', params);
};
export const createEspOrder = (params: { shopId: string; collectShopName: string }) => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.submitAndCreateEspOrder', params);
};

/**
 * 一键建群接口
 */
export const createOptGroup = (params: {
  pid: string;
  bizSource: 'leads' | 'operate';
}): Promise<{
  split: any;
  match: any;
  includes: any;
  success: boolean;
  message?: string;
}> => {
  return gdRequest('amap-sales-automation.GroupManagerFacade.createOptGroup', params);
};

// 查询本地ESP订单信息
export const queryLocalOptEspWoosOrder = (params: {
  shopId: string;
  auditId?: string;
}): Promise<EspAuditTypes.AuditRecord> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryLocalOptEspOrder', params);
};
// 保存无法提交原因
export const saveUnableCommitReason = (params: {
  shopId: string;
  unableSubmitReason: EspAuditTypes.UnableSubmitReason;
}): Promise<void> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.saveUnableSubmitReason', params);
};

// 查询无法提交原因
export const queryUnableCommitReason = (params: {
  shopId: string;
}): Promise<EspAuditTypes.AuditUnableCommitRecord> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryUnableSubmitReason', {
    shopId: params.shopId,
  });
};

// 获取任务完成率
export const getTaskCompletionRate = (): Promise<ITaskCompletionRateData> => {
  return gdRequest('amap-sales-operation.OptInstrumentQueryFacade.queryTaskCompletionRate');
};
